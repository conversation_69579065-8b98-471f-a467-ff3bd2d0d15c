using Orleans;
using Curio.Shared.Users;
using Curio.Shared.Admins;

namespace Curio.Events.State;

[GenerateSerializer]
public class RoleState
{
    [Id(0)] public string Id { get; set; } = string.Empty;
    [Id(1)] public string Name { get; set; } = string.Empty;
    [Id(2)] public string Description { get; set; } = string.Empty;
    [Id(3)] public DateTime CreatedAt { get; set; }
    [Id(4)] public DateTime? UpdatedAt { get; set; }
    [Id(5)] public string CreatedBy { get; set; } = string.Empty;
    [Id(6)] public bool IsActive { get; set; } = true;
    [Id(7)] public List<PermissionInfo> Permissions { get; set; } = new();
    [Id(8)] public HashSet<string> ProcessedCommands { get; set; } = new();

    // 业务规则验证
    public bool CanAssignPermission(PermissionResource resource, PermissionAction action)
    {
        return IsActive && !string.IsNullOrEmpty(Name);
    }

    public bool HasPermission(PermissionResource resource, PermissionAction action)
    {
        return Permissions.Any(p => p.Resource == resource && p.Action == action);
    }

    public bool CanDelete()
    {
        // 系统内置角色不能删除
        return IsActive && !IsBuiltInRole();
    }

    private bool IsBuiltInRole()
    {
        // 定义系统内置角色
        var builtInRoles = new[] { "SuperAdmin", "SystemAdmin", "Viewer" };
        return builtInRoles.Contains(Name);
    }

    // 状态转换方法
    public void ApplyEvent(RoleCreatedEvent evt)
    {
        Id = evt.RoleId;
        Name = evt.RoleName;
        Description = evt.Description;
        CreatedAt = evt.CreatedAt;
        CreatedBy = evt.CreatedBy;
        IsActive = true;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(RoleUpdatedEvent evt)
    {
        Name = evt.RoleName;
        Description = evt.Description;
        UpdatedAt = evt.UpdatedAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(RolePermissionAssignedEvent evt)
    {
        var permission = new PermissionInfo
        {
            Resource = evt.Resource,
            Action = evt.Action,
            AssignedAt = evt.AssignedAt,
            AssignedBy = evt.AssignedBy
        };

        // 避免重复添加相同权限
        if (!HasPermission(evt.Resource, evt.Action))
        {
            Permissions.Add(permission);
        }

        UpdatedAt = evt.AssignedAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(RolePermissionRemovedEvent evt)
    {
        Permissions.RemoveAll(p => p.Resource == evt.Resource && p.Action == evt.Action);
        UpdatedAt = evt.RemovedAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(RoleDeletedEvent evt)
    {
        IsActive = false;
        UpdatedAt = evt.DeletedAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    // 转换为DTO
    public RoleDto ToDto()
    {
        return new RoleDto
        {
            Id = Id,
            Name = Name,
            Description = Description,
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt,
            CreatedBy = CreatedBy,
            IsActive = IsActive,
            Permissions = Permissions.Select(p => new PermissionDto
            {
                Resource = p.Resource,
                Action = p.Action,
                AssignedAt = p.AssignedAt,
                AssignedBy = p.AssignedBy
            }).ToList(),
            PermissionCount = Permissions.Count,
            IsBuiltIn = IsBuiltInRole()
        };
    }
}

[GenerateSerializer]
public class PermissionInfo
{
    [Id(0)] public PermissionResource Resource { get; set; }
    [Id(1)] public PermissionAction Action { get; set; }
    [Id(2)] public DateTime AssignedAt { get; set; }
    [Id(3)] public string AssignedBy { get; set; } = string.Empty;
}
