using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Orleans;
using Curio.Orleans.Interfaces.Admins;
using Curio.Shared.Users;
using Curio.Api.Models;
using Curio.Api.Constants;

namespace Curio.Api.Authorization;

/// <summary>
/// 权限验证特性
/// 用于控制器和操作方法的权限验证
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class RequirePermissionAttribute : Attribute, IAsyncAuthorizationFilter
{
    public PermissionResource Resource { get; }
    public PermissionAction Action { get; }
    public bool AllowAnonymous { get; set; } = false;

    public RequirePermissionAttribute(PermissionResource resource, PermissionAction action)
    {
        Resource = resource;
        Action = action;
    }

    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        try
        {
            // 检查是否允许匿名访问
            if (AllowAnonymous)
            {
                return;
            }

            // 从JWT Token中获取当前用户ID
            var currentAdminId = GetCurrentAdminId(context);

            if (string.IsNullOrEmpty(currentAdminId))
            {
                context.Result = new UnauthorizedObjectResult(ApiResponse.CreateError("Authentication required", ApiCodes.MissingToken));
                return;
            }

            // 获取Orleans GrainFactory
            var grainFactory = context.HttpContext.RequestServices.GetRequiredService<IGrainFactory>();
            var permissionGrain = grainFactory.GetGrain<IPermissionQueryGrain>("permission-query");

            // 检查权限
            var hasPermission = await permissionGrain.HasPermissionAsync(currentAdminId, Resource, Action);

            if (!hasPermission)
            {
                context.Result = new ForbidResult();
                var errorResponse = ApiResponse.CreateError(
                    $"Insufficient permissions. Required: {Resource}:{Action}",
                    ApiCodes.InsufficientPermissions
                );
                context.Result = new ObjectResult(errorResponse) { StatusCode = 403 };
                return;
            }

            // 权限验证通过，继续执行
        }
        catch (Exception ex)
        {
            // 记录错误日志
            var logger = context.HttpContext.RequestServices.GetService<ILogger<RequirePermissionAttribute>>();
            logger?.LogError(ex, "Error during permission authorization");

            // 出现错误时拒绝访问
            context.Result = new ObjectResult(ApiResponse.CreateError("Authorization failed", ApiCodes.InternalServerError))
            {
                StatusCode = 500
            };
        }
    }

    private static string? GetCurrentAdminId(AuthorizationFilterContext context)
    {
        // 从JWT Claims中获取用户ID
        var user = context.HttpContext.User;
        if (user?.Identity?.IsAuthenticated == true)
        {
            // 从Claims中提取管理员ID
            var adminIdClaim = user.FindFirst("admin_id") ?? user.FindFirst("sub");
            return adminIdClaim?.Value;
        }

        return null;
    }
}
