using Microsoft.AspNetCore.Mvc;
using Curio.Projections.Handlers;
using Curio.Projections.Interfaces;
using Curio.Api.Models;

namespace Curio.Api.Controllers;

/// <summary>
/// 投影控制器 - 提供基于Orleans的实时投影查询API
/// 充分利用Orleans的分布式能力，提供高性能的数据查询
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ProjectionsController : BaseController
{
    private readonly ProjectionQueryHandler _queryHandler;
    private readonly ILogger<ProjectionsController> _logger;

    public ProjectionsController(ProjectionQueryHandler queryHandler, ILogger<ProjectionsController> logger)
    {
        _queryHandler = queryHandler;
        _logger = logger;
    }

    /// <summary>
    /// 获取全局用户统计信息
    /// </summary>
    /// <returns>用户统计数据</returns>
    [HttpGet("users/statistics/global")]
    public async Task<ActionResult<ApiResponse<UserStatistics>>> GetGlobalUserStatistics()
    {
        try
        {
            var statistics = await _queryHandler.GetGlobalUserStatisticsAsync();
            return Success(statistics, "Global user statistics retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting global user statistics");
            return InternalError<UserStatistics>("Failed to retrieve global user statistics");
        }
    }

    /// <summary>
    /// 获取特定域名的用户统计
    /// </summary>
    /// <param name="domain">邮箱域名</param>
    /// <returns>域名统计数据</returns>
    [HttpGet("users/statistics/domain/{domain}")]
    public async Task<ActionResult<ApiResponse<DomainStatistics>>> GetDomainStatistics(string domain)
    {
        try
        {
            var statistics = await _queryHandler.GetDomainStatisticsAsync(domain);
            return Success(statistics, $"Domain statistics for {domain} retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting domain statistics for {Domain}", domain);
            return InternalError<DomainStatistics>($"Failed to retrieve statistics for domain {domain}");
        }
    }

    /// <summary>
    /// 获取多个域名的统计信息（并行查询）
    /// </summary>
    /// <param name="domains">域名列表（逗号分隔）</param>
    /// <returns>多域名统计数据</returns>
    [HttpGet("users/statistics/domains")]
    public async Task<ActionResult<ApiResponse<Dictionary<string, DomainStatistics>>>> GetMultipleDomainStatistics([FromQuery] string domains)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(domains))
            {
                return BadRequest(ErrorNoData("Domains parameter is required", 400));
            }

            var domainList = domains.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                   .Select(d => d.Trim())
                                   .Where(d => !string.IsNullOrEmpty(d));

            var statistics = await _queryHandler.GetMultipleDomainStatisticsAsync(domainList);
            return Success(statistics, "Multiple domain statistics retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting multiple domain statistics for {Domains}", domains);
            return InternalError<Dictionary<string, DomainStatistics>>("Failed to retrieve multiple domain statistics");
        }
    }

    /// <summary>
    /// 获取投影系统健康状态
    /// </summary>
    /// <returns>投影系统健康信息</returns>
    [HttpGet("health")]
    public async Task<ActionResult<ApiResponse<ProjectionHealthStatus>>> GetProjectionHealth()
    {
        try
        {
            var health = await _queryHandler.GetProjectionHealthAsync();
            return Success(health, "Projection health status retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting projection health status");
            return InternalError<ProjectionHealthStatus>("Failed to retrieve projection health status");
        }
    }
}
