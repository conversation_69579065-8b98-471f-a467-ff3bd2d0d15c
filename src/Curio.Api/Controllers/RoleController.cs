using Microsoft.AspNetCore.Mvc;
using Curio.Application.Interfaces;
using Curio.Shared.Admins;
using Curio.Api.Models;
using Curio.Api.Constants;

namespace Curio.Api.Controllers;

[ApiController]
[Route("api/admin/roles")]
public class RoleController : BaseController
{
    private readonly IAdminService _adminService;
    private readonly ILogger<RoleController> _logger;

    public RoleController(IAdminService adminService, ILogger<RoleController> logger)
    {
        _adminService = adminService;
        _logger = logger;
    }

    /// <summary>
    /// 获取角色信息
    /// </summary>
    [HttpGet("{roleId}")]
    public async Task<ActionResult<ApiResponse<RoleDto>>> GetRole(string roleId)
    {
        try
        {
            var role = await _adminService.GetRoleAsync(roleId);

            if (role == null)
            {
                return NotFoundError<RoleDto>($"Role with ID '{roleId}' not found");
            }

            return Success(role, "Role retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role: {RoleId}", roleId);
            return InternalError<RoleDto>("Failed to retrieve role");
        }
    }

    /// <summary>
    /// 创建角色
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> CreateRole([FromBody] CreateRoleCommand command)
    {
        try
        {
            command.CreatedBy = "system"; // TODO: 从Token中获取

            var result = await _adminService.CreateRoleAsync(command);

            if (result.Success)
            {
                return Created(result, "Role created successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to create role", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role: {RoleName}", command.RoleName);
            return InternalError<RoleOperationResult>("Failed to create role");
        }
    }

    /// <summary>
    /// 更新角色信息
    /// </summary>
    [HttpPut("{roleId}")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> UpdateRole(string roleId, [FromBody] UpdateRoleCommand command)
    {
        try
        {
            command.RoleId = roleId;
            command.UpdatedBy = "system"; // TODO: 从Token中获取

            var result = await _adminService.UpdateRoleAsync(command);

            if (result.Success)
            {
                return Success(result, "Role updated successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to update role", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to update role");
        }
    }

    /// <summary>
    /// 删除角色
    /// </summary>
    [HttpDelete("{roleId}")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> DeleteRole(string roleId)
    {
        try
        {
            var command = new DeleteRoleCommand
            {
                RoleId = roleId,
                DeletedBy = "system" // TODO: 从Token中获取
            };

            var result = await _adminService.DeleteRoleAsync(command);

            if (result.Success)
            {
                return Success(result, "Role deleted successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to delete role", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to delete role");
        }
    }

    /// <summary>
    /// 获取角色权限
    /// </summary>
    [HttpGet("{roleId}/permissions")]
    public async Task<ActionResult<ApiResponse<List<PermissionDto>>>> GetRolePermissions(string roleId)
    {
        try
        {
            var permissions = await _adminService.GetRolePermissionsAsync(roleId);
            return Success(permissions, "Role permissions retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role permissions: {RoleId}", roleId);
            return InternalError<List<PermissionDto>>("Failed to retrieve role permissions");
        }
    }

    /// <summary>
    /// 分配权限到角色
    /// </summary>
    [HttpPost("{roleId}/permissions")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> AssignPermission(string roleId, [FromBody] AssignPermissionCommand command)
    {
        try
        {
            command.RoleId = roleId;
            command.AssignedBy = "system"; // TODO: 从Token中获取

            var result = await _adminService.AssignPermissionAsync(command);

            if (result.Success)
            {
                return Success(result, "Permission assigned successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to assign permission", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permission: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to assign permission");
        }
    }

    /// <summary>
    /// 从角色移除权限
    /// </summary>
    [HttpDelete("{roleId}/permissions")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> RemovePermission(string roleId, [FromBody] RemovePermissionCommand command)
    {
        try
        {
            command.RoleId = roleId;
            command.RemovedBy = "system"; // TODO: 从Token中获取

            var result = await _adminService.RemovePermissionAsync(command);

            if (result.Success)
            {
                return Success(result, "Permission removed successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to remove permission", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing permission: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to remove permission");
        }
    }

    /// <summary>
    /// 批量分配权限到角色
    /// </summary>
    [HttpPost("{roleId}/permissions/batch-assign")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> AssignPermissions(string roleId, [FromBody] BatchPermissionRequest request)
    {
        try
        {
            var assignedBy = "system"; // TODO: 从Token中获取
            var result = await _adminService.AssignPermissionsAsync(roleId, request.Permissions, assignedBy);

            if (result.Success)
            {
                return Success(result, "Permissions assigned successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to assign permissions", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch assigning permissions: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to assign permissions");
        }
    }

    /// <summary>
    /// 批量移除角色权限
    /// </summary>
    [HttpPost("{roleId}/permissions/batch-remove")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> RemovePermissions(string roleId, [FromBody] BatchPermissionRequest request)
    {
        try
        {
            var removedBy = "system"; // TODO: 从Token中获取
            var result = await _adminService.RemovePermissionsAsync(roleId, request.Permissions, removedBy);

            if (result.Success)
            {
                return Success(result, "Permissions removed successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to remove permissions", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch removing permissions: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to remove permissions");
        }
    }

    /// <summary>
    /// 搜索角色
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<RoleSummaryDto>>>> SearchRoles(
        [FromQuery] string? keyword = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] int skip = 0,
        [FromQuery] int take = 20)
    {
        try
        {
            var result = await _adminService.SearchRolesAsync(keyword, isActive, skip, take);
            return Success(result, "Role search completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching roles");
            return InternalError<PagedResult<RoleSummaryDto>>("Failed to search roles");
        }
    }

    /// <summary>
    /// 获取角色统计信息
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<ApiResponse<RoleStatsDto>>> GetRoleStats()
    {
        try
        {
            var stats = await _adminService.GetRoleStatsAsync();
            return Success(stats, "Role statistics retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role stats");
            return InternalError<RoleStatsDto>("Failed to retrieve role statistics");
        }
    }

    /// <summary>
    /// 获取所有可用权限
    /// </summary>
    [HttpGet("permissions/available")]
    public async Task<ActionResult<ApiResponse<List<PermissionGroupDto>>>> GetAvailablePermissions()
    {
        try
        {
            var permissions = await _adminService.GetAllPermissionsAsync();
            return Success(permissions, "Available permissions retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available permissions");
            return InternalError<List<PermissionGroupDto>>("Failed to retrieve available permissions");
        }
    }

    /// <summary>
    /// 获取权限分组
    /// </summary>
    [HttpGet("permissions/groups")]
    public async Task<ActionResult<ApiResponse<List<PermissionGroupDto>>>> GetPermissionGroups()
    {
        try
        {
            var groups = await _adminService.GetPermissionGroupsAsync();
            return Success(groups, "Permission groups retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permission groups");
            return InternalError<List<PermissionGroupDto>>("Failed to retrieve permission groups");
        }
    }
}

// 辅助请求模型
public class BatchPermissionRequest
{
    public List<PermissionAssignment> Permissions { get; set; } = new();
}
