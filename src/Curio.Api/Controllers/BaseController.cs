using Microsoft.AspNetCore.Mvc;
using Curio.Api.Models;
using Curio.Api.Constants;

namespace Curio.Api.Controllers;

[ApiController]
public abstract class BaseController : ControllerBase
{
    /// <summary>
    /// 创建成功响应
    /// </summary>
    protected ActionResult<ApiResponse<T>> Success<T>(T data, string message = "Success", int code = ApiCodes.Success)
    {
        var response = ApiResponse<T>.CreateSuccess(data, message, code);
        return Ok(response);
    }

    /// <summary>
    /// 创建成功响应（无数据）
    /// </summary>
    protected ActionResult<ApiResponse> Success(string message = "Success", int code = ApiCodes.Success)
    {
        var response = ApiResponse.CreateSuccess(message, code);
        return Ok(response);
    }

    /// <summary>
    /// 创建创建成功响应
    /// </summary>
    protected ActionResult<ApiResponse<T>> Created<T>(T data, string message = "Created successfully")
    {
        var response = ApiResponse<T>.CreateSuccess(data, message, ApiCodes.Created);
        return StatusCode(201, response);
    }

    /// <summary>
    /// 创建删除成功响应
    /// </summary>
    protected ActionResult<ApiResponse> Deleted(string message = "Deleted successfully")
    {
        var response = ApiResponse.CreateSuccess(message, ApiCodes.Deleted);
        return Ok(response);
    }

    /// <summary>
    /// 创建错误响应
    /// </summary>
    protected ActionResult<ApiResponse<T>> Error<T>(string message, int code, int httpStatusCode = 400)
    {
        var response = ApiResponse<T>.CreateError(message, code);
        return StatusCode(httpStatusCode, response);
    }

    /// <summary>
    /// 创建错误响应（无数据）
    /// </summary>
    protected ActionResult<ApiResponse> ErrorNoData(string message, int code, int httpStatusCode = 400)
    {
        var response = ApiResponse.CreateError(message, code);
        return StatusCode(httpStatusCode, response);
    }

    /// <summary>
    /// 创建验证失败响应
    /// </summary>
    protected ActionResult<ApiResponse<T>> ValidationError<T>(string message = "Validation failed")
    {
        return Error<T>(message, ApiCodes.ValidationFailed, 400);
    }

    /// <summary>
    /// 创建资源不存在响应
    /// </summary>
    protected ActionResult<ApiResponse<T>> NotFoundError<T>(string message = "Resource not found")
    {
        return Error<T>(message, ApiCodes.ResourceNotFound, 404);
    }

    /// <summary>
    /// 创建业务规则验证失败响应
    /// </summary>
    protected ActionResult<ApiResponse<T>> BusinessError<T>(string message, int code = ApiCodes.BusinessRuleValidationFailed)
    {
        return Error<T>(message, code, 422);
    }

    /// <summary>
    /// 创建内部服务器错误响应
    /// </summary>
    protected ActionResult<ApiResponse<T>> InternalError<T>(string message = "Internal server error")
    {
        return Error<T>(message, ApiCodes.InternalServerError, 500);
    }

    /// <summary>
    /// 处理结果并返回统一格式
    /// </summary>
    protected ActionResult<ApiResponse<T>> HandleResult<T>(T? result, string? notFoundMessage = null)
    {
        if (result == null)
        {
            return NotFoundError<T>(notFoundMessage ?? "Resource not found");
        }

        return Success(result);
    }

    /// <summary>
    /// 处理布尔结果并返回统一格式
    /// </summary>
    protected ActionResult<ApiResponse<bool>> HandleBoolResult(bool success, string? errorMessage = null, string? successMessage = null)
    {
        if (success)
        {
            return Success(success, successMessage ?? "Operation completed successfully");
        }

        return ValidationError<bool>(errorMessage ?? "Operation failed");
    }
}
