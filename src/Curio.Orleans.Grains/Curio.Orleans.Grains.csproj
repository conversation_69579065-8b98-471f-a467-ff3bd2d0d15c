﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Curio.Orleans.Interfaces\Curio.Orleans.Interfaces.csproj" />
    <ProjectReference Include="..\Curio.Shared\Curio.Shared.csproj" />
    <ProjectReference Include="..\Curio.Events\Curio.Events.csproj" />
    <ProjectReference Include="..\Curio.Infrastructure\Curio.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Orleans.Core" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.EventSourcing" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Streaming" Version="9.2.1" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
