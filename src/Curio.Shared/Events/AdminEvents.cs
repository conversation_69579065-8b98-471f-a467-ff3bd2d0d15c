using Orleans;
using Curio.Shared.Users;

namespace Curio.Shared.Admins;

// === Admin 相关领域事件 ===

[GenerateSerializer]
public class AdminCreatedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string Username { get; set; } = string.Empty;
    [Id(2)] public string Email { get; set; } = string.Empty;
    [Id(3)] public string Name { get; set; } = string.Empty;
    [Id(4)] public DateTime CreatedAt { get; set; }
    [Id(5)] public string CreatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class AdminPasswordChangedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string PasswordHash { get; set; } = string.Empty;
    [Id(2)] public DateTime ChangedAt { get; set; }
    [Id(3)] public bool IsInitialSetup { get; set; }
}

[GenerateSerializer]
public class AdminLoginAttemptedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string Username { get; set; } = string.Empty;
    [Id(2)] public bool Success { get; set; }
    [Id(3)] public string? FailureReason { get; set; }
    [Id(4)] public string IpAddress { get; set; } = string.Empty;
    [Id(5)] public string UserAgent { get; set; } = string.Empty;
    [Id(6)] public DateTime AttemptedAt { get; set; }
    [Id(7)] public bool RequiredTwoFactor { get; set; }
}

[GenerateSerializer]
public class AdminUpdatedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string Email { get; set; } = string.Empty;
    [Id(2)] public string Name { get; set; } = string.Empty;
    [Id(3)] public DateTime UpdatedAt { get; set; }
    [Id(4)] public string UpdatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class AdminStatusChangedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public AdminStatus OldStatus { get; set; }
    [Id(2)] public AdminStatus NewStatus { get; set; }
    [Id(3)] public string Reason { get; set; } = string.Empty;
    [Id(4)] public DateTime ChangedAt { get; set; }
    [Id(5)] public string ChangedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class AdminRoleAssignedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string RoleId { get; set; } = string.Empty;
    [Id(2)] public string RoleName { get; set; } = string.Empty;
    [Id(3)] public DateTime AssignedAt { get; set; }
    [Id(4)] public string AssignedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class AdminRoleRemovedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string RoleId { get; set; } = string.Empty;
    [Id(2)] public string RoleName { get; set; } = string.Empty;
    [Id(3)] public DateTime RemovedAt { get; set; }
    [Id(4)] public string RemovedBy { get; set; } = string.Empty;
}

// === 2FA 相关事件 ===

[GenerateSerializer]
public class TwoFactorEnabledEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string SecretKey { get; set; } = string.Empty;
    [Id(2)] public DateTime EnabledAt { get; set; }
    [Id(3)] public List<string> RecoveryCodes { get; set; } = new();
}

[GenerateSerializer]
public class TwoFactorDisabledEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public DateTime DisabledAt { get; set; }
    [Id(2)] public string DisabledBy { get; set; } = string.Empty;
    [Id(3)] public string Reason { get; set; } = string.Empty;
}

[GenerateSerializer]
public class TwoFactorCodeVerifiedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public bool Success { get; set; }
    [Id(2)] public DateTime VerifiedAt { get; set; }
    [Id(3)] public string IpAddress { get; set; } = string.Empty;
    [Id(4)] public string? FailureReason { get; set; }
}

[GenerateSerializer]
public class RecoveryCodeUsedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string RecoveryCode { get; set; } = string.Empty;
    [Id(2)] public DateTime UsedAt { get; set; }
    [Id(3)] public string IpAddress { get; set; } = string.Empty;
}

// === Role 相关领域事件 ===

[GenerateSerializer]
public class RoleCreatedEvent : DomainEvent
{
    [Id(0)] public string RoleId { get; set; } = string.Empty;
    [Id(1)] public string RoleName { get; set; } = string.Empty;
    [Id(2)] public string Description { get; set; } = string.Empty;
    [Id(3)] public DateTime CreatedAt { get; set; }
    [Id(4)] public string CreatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class RoleUpdatedEvent : DomainEvent
{
    [Id(0)] public string RoleId { get; set; } = string.Empty;
    [Id(1)] public string RoleName { get; set; } = string.Empty;
    [Id(2)] public string Description { get; set; } = string.Empty;
    [Id(3)] public DateTime UpdatedAt { get; set; }
    [Id(4)] public string UpdatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class RoleDeletedEvent : DomainEvent
{
    [Id(0)] public string RoleId { get; set; } = string.Empty;
    [Id(1)] public string RoleName { get; set; } = string.Empty;
    [Id(2)] public DateTime DeletedAt { get; set; }
    [Id(3)] public string DeletedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class RolePermissionAssignedEvent : DomainEvent
{
    [Id(0)] public string RoleId { get; set; } = string.Empty;
    [Id(1)] public PermissionResource Resource { get; set; }
    [Id(2)] public PermissionAction Action { get; set; }
    [Id(3)] public DateTime AssignedAt { get; set; }
    [Id(4)] public string AssignedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class RolePermissionRemovedEvent : DomainEvent
{
    [Id(0)] public string RoleId { get; set; } = string.Empty;
    [Id(1)] public PermissionResource Resource { get; set; }
    [Id(2)] public PermissionAction Action { get; set; }
    [Id(3)] public DateTime RemovedAt { get; set; }
    [Id(4)] public string RemovedBy { get; set; } = string.Empty;
}

// === 审计相关事件 ===

[GenerateSerializer]
public class AdminAuditEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string AdminUsername { get; set; } = string.Empty;
    [Id(2)] public AuditActionType ActionType { get; set; }
    [Id(3)] public string ActionDescription { get; set; } = string.Empty;
    [Id(4)] public PermissionResource? Resource { get; set; }
    [Id(5)] public string? ResourceId { get; set; }
    [Id(6)] public AuditLevel Level { get; set; }
    [Id(7)] public new DateTime Timestamp { get; set; }
    [Id(8)] public string IpAddress { get; set; } = string.Empty;
    [Id(9)] public string UserAgent { get; set; } = string.Empty;
    [Id(10)] public string? Details { get; set; }
    [Id(11)] public bool Success { get; set; }
    [Id(12)] public string? ErrorMessage { get; set; }
}

[GenerateSerializer]
public class SecurityAlertEvent : DomainEvent
{
    [Id(0)] public string AlertType { get; set; } = string.Empty;
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string Description { get; set; } = string.Empty;
    [Id(3)] public AuditLevel Severity { get; set; }
    [Id(4)] public DateTime DetectedAt { get; set; }
    [Id(5)] public string IpAddress { get; set; } = string.Empty;
    [Id(6)] public Dictionary<string, string> Metadata { get; set; } = new();
}

// === 系统维护事件 ===

[GenerateSerializer]
public class AdminSessionCreatedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string SessionId { get; set; } = string.Empty;
    [Id(2)] public DateTime CreatedAt { get; set; }
    [Id(3)] public DateTime ExpiresAt { get; set; }
    [Id(4)] public string IpAddress { get; set; } = string.Empty;
    [Id(5)] public string UserAgent { get; set; } = string.Empty;
}

[GenerateSerializer]
public class AdminSessionTerminatedEvent : DomainEvent
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public string SessionId { get; set; } = string.Empty;
    [Id(2)] public DateTime TerminatedAt { get; set; }
    [Id(3)] public string Reason { get; set; } = string.Empty;
    [Id(4)] public bool IsExpired { get; set; }
}
